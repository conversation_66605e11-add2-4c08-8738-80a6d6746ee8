-- =====================================================
-- INSIGHTS QUERIES FOR ACADEMIC MANAGEMENT SYSTEM
-- =====================================================

-- =====================================================
-- REQUIREMENT 1: STUDENT INCHARGE, SUBJECT TEACHERS, SUBJECTS, CLASS, TRADE, SECTION, ROOM NO DETAILS
-- =====================================================

-- Query 1.1: Complete Student-Teacher-Subject-Class Mapping
-- This query provides comprehensive details about students, their class incharge, subject teachers, and classroom details
SELECT DISTINCT
    -- Student Information
    s.id as student_id,
    s.student_id as student_roll_id,
    s.name as student_name,
    s.class as student_class,
    s.section as student_section,
    s.trade as student_trade,
    s.session as academic_session,
    s.room_number as student_room_number,
    
    -- Class Information
    c.id as class_id,
    c.name as class_name,
    c.grade as class_grade,
    
    -- Classroom Information
    cr.id as classroom_id,
    cr.room_id,
    r.room_number as classroom_room_number,
    r.capacity as room_capacity,
    r.building,
    r.floor,
    
    -- Class Incharge Information
    ci_user.id as class_incharge_id,
    ci_user.name as class_incharge_name,
    ci_user.email as class_incharge_email,
    
    -- Subject Information
    sub.id as subject_id,
    sub.name as subject_name,
    sub.code as subject_code,
    sub.subject_group,
    
    -- Subject Teacher Information
    st_user.id as subject_teacher_id,
    st_user.name as subject_teacher_name,
    st_user.email as subject_teacher_email,
    
    -- Additional Details
    CASE 
        WHEN s.room_number = r.room_number THEN 'Matched'
        ELSE 'Different'
    END as room_assignment_status

FROM students s
    -- Join with classes
    LEFT JOIN classes c ON s.class = c.grade
    
    -- Join with classrooms to get room details
    LEFT JOIN classrooms cr ON (
        c.id = cr.class_id 
        AND s.section = cr.section 
        AND s.session = cr.session
    )
    LEFT JOIN rooms r ON cr.room_id = r.id
    
    -- Join with class incharge
    LEFT JOIN class_incharge ci ON c.id = ci.class_id
    LEFT JOIN users ci_user ON ci.teacher_id = ci_user.id
    
    -- Join with student subjects to get all subjects for the student
    LEFT JOIN student_subjects ss ON s.id = ss.student_id
    LEFT JOIN subjects sub ON ss.subject_id = sub.id
    
    -- Join with teacher subjects to find subject teachers
    LEFT JOIN teacher_subjects ts ON sub.id = ts.subject_id
    LEFT JOIN users st_user ON ts.teacher_id = st_user.id AND st_user.role = 'teacher'
    
    -- Join with teacher classes to ensure teacher teaches this class
    LEFT JOIN teacher_classes tc ON (
        st_user.id = tc.teacher_id 
        AND cr.id = tc.classroom_id
    )

WHERE s.is_active = TRUE
    AND (c.is_active IS NULL OR c.is_active = TRUE)
    AND (cr.is_active IS NULL OR cr.is_active = TRUE)
    AND (ci_user.is_active IS NULL OR ci_user.is_active = TRUE)
    AND (st_user.is_active IS NULL OR st_user.is_active = TRUE)

ORDER BY s.class, s.section, s.name, sub.name;

-- Query 1.2: Class-wise Summary with Incharge and Room Details
SELECT 
    c.grade as class_grade,
    cr.section,
    t.name as trade_name,
    cr.session as academic_session,
    
    -- Room Information
    r.room_number,
    r.capacity as room_capacity,
    r.building,
    r.floor,
    
    -- Class Incharge
    ci_user.name as class_incharge_name,
    ci_user.email as class_incharge_email,
    
    -- Student Count
    COUNT(DISTINCT s.id) as total_students,
    
    -- Subject Count
    COUNT(DISTINCT sub.id) as total_subjects,
    
    -- Teacher Count
    COUNT(DISTINCT ts.teacher_id) as total_subject_teachers

FROM classes c
    LEFT JOIN classrooms cr ON c.id = cr.class_id
    LEFT JOIN trades t ON cr.trade_id = t.id
    LEFT JOIN rooms r ON cr.room_id = r.id
    LEFT JOIN class_incharge ci ON c.id = ci.class_id
    LEFT JOIN users ci_user ON ci.teacher_id = ci_user.id
    LEFT JOIN students s ON (
        s.class = c.grade 
        AND s.section = cr.section 
        AND s.session = cr.session
        AND s.is_active = TRUE
    )
    LEFT JOIN student_subjects ss ON s.id = ss.student_id
    LEFT JOIN subjects sub ON ss.subject_id = sub.id
    LEFT JOIN teacher_subjects ts ON sub.id = ts.subject_id

WHERE (c.is_active IS NULL OR c.is_active = TRUE)
    AND (cr.is_active IS NULL OR cr.is_active = TRUE)

GROUP BY c.id, cr.id, t.id, r.id, ci_user.id
ORDER BY c.grade, cr.section;

-- Query 1.3: Subject-wise Teacher Assignment Details
SELECT 
    sub.name as subject_name,
    sub.code as subject_code,
    sub.subject_group,
    
    -- Teacher Information
    u.name as teacher_name,
    u.email as teacher_email,
    
    -- Classes taught by this teacher for this subject
    GROUP_CONCAT(
        DISTINCT CONCAT(c.grade, '-', cr.section, ' (', t.name, ')')
        ORDER BY c.grade, cr.section
        SEPARATOR ', '
    ) as classes_taught,
    
    -- Room numbers where this subject is taught
    GROUP_CONCAT(
        DISTINCT r.room_number
        ORDER BY r.room_number
        SEPARATOR ', '
    ) as room_numbers,
    
    -- Student count for this subject-teacher combination
    COUNT(DISTINCT s.id) as total_students

FROM subjects sub
    LEFT JOIN teacher_subjects ts ON sub.id = ts.subject_id
    LEFT JOIN users u ON ts.teacher_id = u.id AND u.role = 'teacher'
    LEFT JOIN teacher_classes tc ON u.id = tc.teacher_id
    LEFT JOIN classrooms cr ON tc.classroom_id = cr.id
    LEFT JOIN classes c ON cr.class_id = c.id
    LEFT JOIN trades t ON cr.trade_id = t.id
    LEFT JOIN rooms r ON cr.room_id = r.id
    LEFT JOIN students s ON (
        s.class = c.grade 
        AND s.section = cr.section 
        AND s.session = cr.session
        AND s.is_active = TRUE
    )
    LEFT JOIN student_subjects ss ON (s.id = ss.student_id AND sub.id = ss.subject_id)

WHERE (u.is_active IS NULL OR u.is_active = TRUE)
    AND (c.is_active IS NULL OR c.is_active = TRUE)
    AND (cr.is_active IS NULL OR cr.is_active = TRUE)

GROUP BY sub.id, u.id
HAVING total_students > 0
ORDER BY sub.name, u.name;

-- =====================================================
-- REQUIREMENT 2: STUDENT FINAL MARKS - CLASS, TRADE, SECTION, SUBJECT WISE 
-- THEORY, CCE, PRACTICAL, TOTAL, PERCENTAGE, GRADE, SESSION
-- =====================================================

-- Note: The following queries assume additional tables for comprehensive marks management
-- If these tables don't exist, they need to be created first

-- Query 2.1: Comprehensive Student Marks Report
-- This query combines marks from different assessment types
SELECT 
    -- Student Information
    s.id as student_id,
    s.student_id as student_roll_id,
    s.name as student_name,
    s.class as student_class,
    s.section as student_section,
    s.trade as student_trade,
    s.session as academic_session,
    
    -- Subject Information
    sub.name as subject_name,
    sub.code as subject_code,
    
    -- Theory Marks (from exam_attempts)
    COALESCE(AVG(ea.score), 0) as theory_marks,
    COALESCE(AVG(ea.total_questions * 1.0), 100) as theory_total_marks,
    
    -- Practical Marks (from practical_records and assignment_submissions)
    COALESCE(AVG(
        CASE 
            WHEN pr.grade = 'A+' THEN 95
            WHEN pr.grade = 'A' THEN 85
            WHEN pr.grade = 'B+' THEN 75
            WHEN pr.grade = 'B' THEN 65
            WHEN pr.grade = 'C+' THEN 55
            WHEN pr.grade = 'C' THEN 45
            WHEN pr.grade = 'D' THEN 35
            ELSE 0
        END
    ), 0) as practical_marks,
    100 as practical_total_marks,
    
    -- Assignment/CCE Marks (from assignment_submissions)
    COALESCE(AVG(
        CASE 
            WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0 
            THEN (asub.marks_obtained * 100.0 / a.total_marks)
            ELSE 0
        END
    ), 0) as cce_marks,
    100 as cce_total_marks,
    
    -- Calculated Total and Percentage
    (
        COALESCE(AVG(ea.score), 0) + 
        COALESCE(AVG(
            CASE 
                WHEN pr.grade = 'A+' THEN 95
                WHEN pr.grade = 'A' THEN 85
                WHEN pr.grade = 'B+' THEN 75
                WHEN pr.grade = 'B' THEN 65
                WHEN pr.grade = 'C+' THEN 55
                WHEN pr.grade = 'C' THEN 45
                WHEN pr.grade = 'D' THEN 35
                ELSE 0
            END
        ), 0) +
        COALESCE(AVG(
            CASE 
                WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0 
                THEN (asub.marks_obtained * 100.0 / a.total_marks)
                ELSE 0
            END
        ), 0)
    ) as total_marks,
    
    300 as total_maximum_marks,
    
    -- Percentage Calculation
    ROUND(
        (
            COALESCE(AVG(ea.score), 0) + 
            COALESCE(AVG(
                CASE 
                    WHEN pr.grade = 'A+' THEN 95
                    WHEN pr.grade = 'A' THEN 85
                    WHEN pr.grade = 'B+' THEN 75
                    WHEN pr.grade = 'B' THEN 65
                    WHEN pr.grade = 'C+' THEN 55
                    WHEN pr.grade = 'C' THEN 45
                    WHEN pr.grade = 'D' THEN 35
                    ELSE 0
                END
            ), 0) +
            COALESCE(AVG(
                CASE 
                    WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0 
                    THEN (asub.marks_obtained * 100.0 / a.total_marks)
                    ELSE 0
                END
            ), 0)
        ) * 100.0 / 300, 2
    ) as percentage,
    
    -- Grade Calculation
    CASE 
        WHEN ROUND(
            (
                COALESCE(AVG(ea.score), 0) + 
                COALESCE(AVG(
                    CASE 
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE 
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0 
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) >= 90 THEN 'A+'
        WHEN ROUND(
            (
                COALESCE(AVG(ea.score), 0) + 
                COALESCE(AVG(
                    CASE 
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE 
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0 
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) >= 80 THEN 'A'
        WHEN ROUND(
            (
                COALESCE(AVG(ea.score), 0) + 
                COALESCE(AVG(
                    CASE 
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE 
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0 
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) >= 70 THEN 'B+'
        WHEN ROUND(
            (
                COALESCE(AVG(ea.score), 0) + 
                COALESCE(AVG(
                    CASE 
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE 
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0 
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) >= 60 THEN 'B'
        WHEN ROUND(
            (
                COALESCE(AVG(ea.score), 0) + 
                COALESCE(AVG(
                    CASE 
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE 
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0 
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) >= 50 THEN 'C+'
        WHEN ROUND(
            (
                COALESCE(AVG(ea.score), 0) + 
                COALESCE(AVG(
                    CASE 
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE 
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0 
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) >= 40 THEN 'C'
        WHEN ROUND(
            (
                COALESCE(AVG(ea.score), 0) + 
                COALESCE(AVG(
                    CASE 
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE 
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0 
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) >= 33 THEN 'D'
        ELSE 'F'
    END as final_grade

FROM students s
    -- Join with student subjects
    LEFT JOIN student_subjects ss ON s.id = ss.student_id
    LEFT JOIN subjects sub ON ss.subject_id = sub.id
    
    -- Join with exam attempts for theory marks
    LEFT JOIN exam_attempts ea ON (
        s.id = ea.user_id 
        AND ea.status = 'completed'
    )
    LEFT JOIN exams e ON ea.exam_id = e.exam_id
    
    -- Join with practical records for practical marks
    LEFT JOIN practical_records pr ON s.id = pr.student_id
    LEFT JOIN practicals p ON pr.practical_id = p.id AND p.subject_id = sub.id
    
    -- Join with assignment submissions for CCE marks
    LEFT JOIN assignment_submissions asub ON s.id = asub.student_id
    LEFT JOIN assignments a ON (
        asub.assignment_id = a.id 
        AND a.subject_id = sub.id
        AND asub.status = 'graded'
    )

WHERE s.is_active = TRUE

GROUP BY s.id, sub.id
ORDER BY s.class, s.section, s.name, sub.name;
