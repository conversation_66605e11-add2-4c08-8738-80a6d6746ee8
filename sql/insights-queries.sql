-- =====================================================
-- INSIGHTS QUERIES FOR ACADEMIC MANAGEMENT SYSTEM
-- =====================================================

-- =====================================================
-- REQUIREMENT 1: STUDENT INCHARGE, SUBJECT TEACHERS, SUBJECTS, CLASS, TRADE, SECTION, ROOM NO DETAILS
-- =====================================================

-- Query 1.1: Complete Student-Teacher-Subject-Class Mapping
-- This query provides comprehensive details about students, their class incharge, subject teachers, and classroom details
SELECT DISTINCT
    -- Student Information
    s.id as student_id,
    s.student_id as student_roll_id,
    s.name as student_name,
    s.class as student_class,
    s.section as student_section,
    s.trade as student_trade,
    s.session as academic_session,
    s.room_number as student_room_number,
    
    -- Class Information
    c.id as class_id,
    c.name as class_name,
    c.grade as class_grade,
    
    -- Classroom Information
    cr.id as classroom_id,
    cr.room_id,
    r.room_number as classroom_room_number,
    r.capacity as room_capacity,
    r.building,
    r.floor,
    
    -- Class Incharge Information
    ci_user.id as class_incharge_id,
    ci_user.name as class_incharge_name,
    ci_user.email as class_incharge_email,
    
    -- Subject Information
    sub.id as subject_id,
    sub.name as subject_name,
    sub.code as subject_code,
    sub.subject_group,
    
    -- Subject Teacher Information
    st_user.id as subject_teacher_id,
    st_user.name as subject_teacher_name,
    st_user.email as subject_teacher_email,
    
    -- Additional Details
    CASE 
        WHEN s.room_number = r.room_number THEN 'Matched'
        ELSE 'Different'
    END as room_assignment_status

FROM students s
    -- Join with classes
    LEFT JOIN classes c ON s.class = c.grade
    
    -- Join with classrooms to get room details
    LEFT JOIN classrooms cr ON (
        c.id = cr.class_id 
        AND s.section = cr.section 
        AND s.session = cr.session
    )
    LEFT JOIN rooms r ON cr.room_id = r.id
    
    -- Join with class incharge
    LEFT JOIN class_incharge ci ON c.id = ci.class_id
    LEFT JOIN users ci_user ON ci.teacher_id = ci_user.id
    
    -- Join with student subjects to get all subjects for the student
    LEFT JOIN student_subjects ss ON s.id = ss.student_id
    LEFT JOIN subjects sub ON ss.subject_id = sub.id
    
    -- Join with teacher subjects to find subject teachers
    LEFT JOIN teacher_subjects ts ON sub.id = ts.subject_id
    LEFT JOIN users st_user ON ts.teacher_id = st_user.id AND st_user.role = 'teacher'
    
    -- Join with teacher classes to ensure teacher teaches this class
    LEFT JOIN teacher_classes tc ON (
        st_user.id = tc.teacher_id 
        AND cr.id = tc.classroom_id
    )

WHERE s.is_active = TRUE
    AND (c.is_active IS NULL OR c.is_active = TRUE)
    AND (cr.is_active IS NULL OR cr.is_active = TRUE)
    AND (ci_user.is_active IS NULL OR ci_user.is_active = TRUE)
    AND (st_user.is_active IS NULL OR st_user.is_active = TRUE)

ORDER BY s.class, s.section, s.name, sub.name;

-- Query 1.2: Class-wise Summary with Incharge and Room Details
SELECT 
    c.grade as class_grade,
    cr.section,
    t.name as trade_name,
    cr.session as academic_session,
    
    -- Room Information
    r.room_number,
    r.capacity as room_capacity,
    r.building,
    r.floor,
    
    -- Class Incharge
    ci_user.name as class_incharge_name,
    ci_user.email as class_incharge_email,
    
    -- Student Count
    COUNT(DISTINCT s.id) as total_students,
    
    -- Subject Count
    COUNT(DISTINCT sub.id) as total_subjects,
    
    -- Teacher Count
    COUNT(DISTINCT ts.teacher_id) as total_subject_teachers

FROM classes c
    LEFT JOIN classrooms cr ON c.id = cr.class_id
    LEFT JOIN trades t ON cr.trade_id = t.id
    LEFT JOIN rooms r ON cr.room_id = r.id
    LEFT JOIN class_incharge ci ON c.id = ci.class_id
    LEFT JOIN users ci_user ON ci.teacher_id = ci_user.id
    LEFT JOIN students s ON (
        s.class = c.grade 
        AND s.section = cr.section 
        AND s.session = cr.session
        AND s.is_active = TRUE
    )
    LEFT JOIN student_subjects ss ON s.id = ss.student_id
    LEFT JOIN subjects sub ON ss.subject_id = sub.id
    LEFT JOIN teacher_subjects ts ON sub.id = ts.subject_id

WHERE (c.is_active IS NULL OR c.is_active = TRUE)
    AND (cr.is_active IS NULL OR cr.is_active = TRUE)

GROUP BY c.id, cr.id, t.id, r.id, ci_user.id
ORDER BY c.grade, cr.section;

-- Query 1.3: Subject-wise Teacher Assignment Details
SELECT 
    sub.name as subject_name,
    sub.code as subject_code,
    sub.subject_group,
    
    -- Teacher Information
    u.name as teacher_name,
    u.email as teacher_email,
    
    -- Classes taught by this teacher for this subject
    GROUP_CONCAT(
        DISTINCT CONCAT(c.grade, '-', cr.section, ' (', t.name, ')')
        ORDER BY c.grade, cr.section
        SEPARATOR ', '
    ) as classes_taught,
    
    -- Room numbers where this subject is taught
    GROUP_CONCAT(
        DISTINCT r.room_number
        ORDER BY r.room_number
        SEPARATOR ', '
    ) as room_numbers,
    
    -- Student count for this subject-teacher combination
    COUNT(DISTINCT s.id) as total_students

FROM subjects sub
    LEFT JOIN teacher_subjects ts ON sub.id = ts.subject_id
    LEFT JOIN users u ON ts.teacher_id = u.id AND u.role = 'teacher'
    LEFT JOIN teacher_classes tc ON u.id = tc.teacher_id
    LEFT JOIN classrooms cr ON tc.classroom_id = cr.id
    LEFT JOIN classes c ON cr.class_id = c.id
    LEFT JOIN trades t ON cr.trade_id = t.id
    LEFT JOIN rooms r ON cr.room_id = r.id
    LEFT JOIN students s ON (
        s.class = c.grade 
        AND s.section = cr.section 
        AND s.session = cr.session
        AND s.is_active = TRUE
    )
    LEFT JOIN student_subjects ss ON (s.id = ss.student_id AND sub.id = ss.subject_id)

WHERE (u.is_active IS NULL OR u.is_active = TRUE)
    AND (c.is_active IS NULL OR c.is_active = TRUE)
    AND (cr.is_active IS NULL OR cr.is_active = TRUE)

GROUP BY sub.id, u.id
HAVING total_students > 0
ORDER BY sub.name, u.name;

-- =====================================================
-- REQUIREMENT 2: STUDENT FINAL MARKS - CLASS, TRADE, SECTION, SUBJECT WISE 
-- THEORY, CCE, PRACTICAL, TOTAL, PERCENTAGE, GRADE, SESSION
-- =====================================================

-- Note: The following queries assume additional tables for comprehensive marks management
-- If these tables don't exist, they need to be created first

-- Query 2.1: Comprehensive Student Marks Report
-- This query combines marks from different assessment types
SELECT 
    -- Student Information
    s.id as student_id,
    s.student_id as student_roll_id,
    s.name as student_name,
    s.class as student_class,
    s.section as student_section,
    s.trade as student_trade,
    s.session as academic_session,
    
    -- Subject Information
    sub.name as subject_name,
    sub.code as subject_code,
    
    -- Theory Marks (from exam_attempts)
    COALESCE(AVG(ea.score), 0) as theory_marks,
    COALESCE(AVG(ea.total_questions * 1.0), 100) as theory_total_marks,
    
    -- Practical Marks (from practical_records and assignment_submissions)
    COALESCE(AVG(
        CASE 
            WHEN pr.grade = 'A+' THEN 95
            WHEN pr.grade = 'A' THEN 85
            WHEN pr.grade = 'B+' THEN 75
            WHEN pr.grade = 'B' THEN 65
            WHEN pr.grade = 'C+' THEN 55
            WHEN pr.grade = 'C' THEN 45
            WHEN pr.grade = 'D' THEN 35
            ELSE 0
        END
    ), 0) as practical_marks,
    100 as practical_total_marks,
    
    -- Assignment/CCE Marks (from assignment_submissions)
    COALESCE(AVG(
        CASE 
            WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0 
            THEN (asub.marks_obtained * 100.0 / a.total_marks)
            ELSE 0
        END
    ), 0) as cce_marks,
    100 as cce_total_marks,
    
    -- Calculated Total and Percentage
    (
        COALESCE(AVG(ea.score), 0) + 
        COALESCE(AVG(
            CASE 
                WHEN pr.grade = 'A+' THEN 95
                WHEN pr.grade = 'A' THEN 85
                WHEN pr.grade = 'B+' THEN 75
                WHEN pr.grade = 'B' THEN 65
                WHEN pr.grade = 'C+' THEN 55
                WHEN pr.grade = 'C' THEN 45
                WHEN pr.grade = 'D' THEN 35
                ELSE 0
            END
        ), 0) +
        COALESCE(AVG(
            CASE 
                WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0 
                THEN (asub.marks_obtained * 100.0 / a.total_marks)
                ELSE 0
            END
        ), 0)
    ) as total_marks,
    
    300 as total_maximum_marks,
    
    -- Percentage Calculation
    ROUND(
        (
            COALESCE(AVG(ea.score), 0) + 
            COALESCE(AVG(
                CASE 
                    WHEN pr.grade = 'A+' THEN 95
                    WHEN pr.grade = 'A' THEN 85
                    WHEN pr.grade = 'B+' THEN 75
                    WHEN pr.grade = 'B' THEN 65
                    WHEN pr.grade = 'C+' THEN 55
                    WHEN pr.grade = 'C' THEN 45
                    WHEN pr.grade = 'D' THEN 35
                    ELSE 0
                END
            ), 0) +
            COALESCE(AVG(
                CASE 
                    WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0 
                    THEN (asub.marks_obtained * 100.0 / a.total_marks)
                    ELSE 0
                END
            ), 0)
        ) * 100.0 / 300, 2
    ) as percentage,
    
    -- Grade Calculation
    CASE 
        WHEN ROUND(
            (
                COALESCE(AVG(ea.score), 0) + 
                COALESCE(AVG(
                    CASE 
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE 
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0 
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) >= 90 THEN 'A+'
        WHEN ROUND(
            (
                COALESCE(AVG(ea.score), 0) + 
                COALESCE(AVG(
                    CASE 
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE 
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0 
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) >= 80 THEN 'A'
        WHEN ROUND(
            (
                COALESCE(AVG(ea.score), 0) + 
                COALESCE(AVG(
                    CASE 
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE 
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0 
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) >= 70 THEN 'B+'
        WHEN ROUND(
            (
                COALESCE(AVG(ea.score), 0) + 
                COALESCE(AVG(
                    CASE 
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE 
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0 
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) >= 60 THEN 'B'
        WHEN ROUND(
            (
                COALESCE(AVG(ea.score), 0) + 
                COALESCE(AVG(
                    CASE 
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE 
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0 
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) >= 50 THEN 'C+'
        WHEN ROUND(
            (
                COALESCE(AVG(ea.score), 0) + 
                COALESCE(AVG(
                    CASE 
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE 
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0 
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) >= 40 THEN 'C'
        WHEN ROUND(
            (
                COALESCE(AVG(ea.score), 0) + 
                COALESCE(AVG(
                    CASE 
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE 
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0 
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) >= 33 THEN 'D'
        ELSE 'F'
    END as final_grade

FROM students s
    -- Join with student subjects
    LEFT JOIN student_subjects ss ON s.id = ss.student_id
    LEFT JOIN subjects sub ON ss.subject_id = sub.id
    
    -- Join with exam attempts for theory marks
    LEFT JOIN exam_attempts ea ON (
        s.id = ea.user_id 
        AND ea.status = 'completed'
    )
    LEFT JOIN exams e ON ea.exam_id = e.exam_id
    
    -- Join with practical records for practical marks
    LEFT JOIN practical_records pr ON s.id = pr.student_id
    LEFT JOIN practicals p ON pr.practical_id = p.id AND p.subject_id = sub.id
    
    -- Join with assignment submissions for CCE marks
    LEFT JOIN assignment_submissions asub ON s.id = asub.student_id
    LEFT JOIN assignments a ON (
        asub.assignment_id = a.id 
        AND a.subject_id = sub.id
        AND asub.status = 'graded'
    )

WHERE s.is_active = TRUE

GROUP BY s.id, sub.id
ORDER BY s.class, s.section, s.name, sub.name;

-- Query 2.2: Class-wise Performance Summary
SELECT
    s.class as student_class,
    s.section as student_section,
    s.trade as student_trade,
    s.session as academic_session,
    sub.name as subject_name,
    sub.code as subject_code,

    -- Aggregate Statistics
    COUNT(DISTINCT s.id) as total_students,
    ROUND(AVG(
        COALESCE(AVG(ea.score), 0) +
        COALESCE(AVG(
            CASE
                WHEN pr.grade = 'A+' THEN 95
                WHEN pr.grade = 'A' THEN 85
                WHEN pr.grade = 'B+' THEN 75
                WHEN pr.grade = 'B' THEN 65
                WHEN pr.grade = 'C+' THEN 55
                WHEN pr.grade = 'C' THEN 45
                WHEN pr.grade = 'D' THEN 35
                ELSE 0
            END
        ), 0) +
        COALESCE(AVG(
            CASE
                WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0
                THEN (asub.marks_obtained * 100.0 / a.total_marks)
                ELSE 0
            END
        ), 0)
    ), 2) as class_average_marks,

    ROUND(AVG(
        (
            COALESCE(AVG(ea.score), 0) +
            COALESCE(AVG(
                CASE
                    WHEN pr.grade = 'A+' THEN 95
                    WHEN pr.grade = 'A' THEN 85
                    WHEN pr.grade = 'B+' THEN 75
                    WHEN pr.grade = 'B' THEN 65
                    WHEN pr.grade = 'C+' THEN 55
                    WHEN pr.grade = 'C' THEN 45
                    WHEN pr.grade = 'D' THEN 35
                    ELSE 0
                END
            ), 0) +
            COALESCE(AVG(
                CASE
                    WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0
                    THEN (asub.marks_obtained * 100.0 / a.total_marks)
                    ELSE 0
                END
            ), 0)
        ) * 100.0 / 300
    ), 2) as class_average_percentage,

    -- Grade Distribution
    SUM(CASE WHEN
        ROUND(
            (
                COALESCE(AVG(ea.score), 0) +
                COALESCE(AVG(
                    CASE
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) >= 90 THEN 1 ELSE 0 END) as grade_a_plus_count,

    SUM(CASE WHEN
        ROUND(
            (
                COALESCE(AVG(ea.score), 0) +
                COALESCE(AVG(
                    CASE
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) >= 80 AND
        ROUND(
            (
                COALESCE(AVG(ea.score), 0) +
                COALESCE(AVG(
                    CASE
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) < 90 THEN 1 ELSE 0 END) as grade_a_count,

    SUM(CASE WHEN
        ROUND(
            (
                COALESCE(AVG(ea.score), 0) +
                COALESCE(AVG(
                    CASE
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) < 33 THEN 1 ELSE 0 END) as failed_count

FROM students s
    LEFT JOIN student_subjects ss ON s.id = ss.student_id
    LEFT JOIN subjects sub ON ss.subject_id = sub.id
    LEFT JOIN exam_attempts ea ON (s.id = ea.user_id AND ea.status = 'completed')
    LEFT JOIN exams e ON ea.exam_id = e.exam_id
    LEFT JOIN practical_records pr ON s.id = pr.student_id
    LEFT JOIN practicals p ON pr.practical_id = p.id AND p.subject_id = sub.id
    LEFT JOIN assignment_submissions asub ON s.id = asub.student_id
    LEFT JOIN assignments a ON (
        asub.assignment_id = a.id
        AND a.subject_id = sub.id
        AND asub.status = 'graded'
    )

WHERE s.is_active = TRUE

GROUP BY s.class, s.section, s.trade, s.session, sub.id
ORDER BY s.class, s.section, sub.name;

-- Query 2.3: Individual Student Report Card
-- This query provides a complete report card for a specific student
SELECT
    -- Student Information
    s.id as student_id,
    s.student_id as student_roll_id,
    s.name as student_name,
    s.class as student_class,
    s.section as student_section,
    s.trade as student_trade,
    s.session as academic_session,
    s.father_name,
    s.mother_name,

    -- Subject-wise Marks
    sub.name as subject_name,
    sub.code as subject_code,

    -- Individual Component Marks
    COALESCE(ROUND(AVG(ea.score), 2), 0) as theory_marks,
    COALESCE(ROUND(AVG(
        CASE
            WHEN pr.grade = 'A+' THEN 95
            WHEN pr.grade = 'A' THEN 85
            WHEN pr.grade = 'B+' THEN 75
            WHEN pr.grade = 'B' THEN 65
            WHEN pr.grade = 'C+' THEN 55
            WHEN pr.grade = 'C' THEN 45
            WHEN pr.grade = 'D' THEN 35
            ELSE 0
        END
    ), 2), 0) as practical_marks,
    COALESCE(ROUND(AVG(
        CASE
            WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0
            THEN (asub.marks_obtained * 100.0 / a.total_marks)
            ELSE 0
        END
    ), 2), 0) as cce_marks,

    -- Total and Grade
    ROUND(
        COALESCE(AVG(ea.score), 0) +
        COALESCE(AVG(
            CASE
                WHEN pr.grade = 'A+' THEN 95
                WHEN pr.grade = 'A' THEN 85
                WHEN pr.grade = 'B+' THEN 75
                WHEN pr.grade = 'B' THEN 65
                WHEN pr.grade = 'C+' THEN 55
                WHEN pr.grade = 'C' THEN 45
                WHEN pr.grade = 'D' THEN 35
                ELSE 0
            END
        ), 0) +
        COALESCE(AVG(
            CASE
                WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0
                THEN (asub.marks_obtained * 100.0 / a.total_marks)
                ELSE 0
            END
        ), 0), 2
    ) as total_marks,

    ROUND(
        (
            COALESCE(AVG(ea.score), 0) +
            COALESCE(AVG(
                CASE
                    WHEN pr.grade = 'A+' THEN 95
                    WHEN pr.grade = 'A' THEN 85
                    WHEN pr.grade = 'B+' THEN 75
                    WHEN pr.grade = 'B' THEN 65
                    WHEN pr.grade = 'C+' THEN 55
                    WHEN pr.grade = 'C' THEN 45
                    WHEN pr.grade = 'D' THEN 35
                    ELSE 0
                END
            ), 0) +
            COALESCE(AVG(
                CASE
                    WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0
                    THEN (asub.marks_obtained * 100.0 / a.total_marks)
                    ELSE 0
                END
            ), 0)
        ) * 100.0 / 300, 2
    ) as percentage,

    CASE
        WHEN ROUND(
            (
                COALESCE(AVG(ea.score), 0) +
                COALESCE(AVG(
                    CASE
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) >= 90 THEN 'A+'
        WHEN ROUND(
            (
                COALESCE(AVG(ea.score), 0) +
                COALESCE(AVG(
                    CASE
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) >= 80 THEN 'A'
        WHEN ROUND(
            (
                COALESCE(AVG(ea.score), 0) +
                COALESCE(AVG(
                    CASE
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) >= 70 THEN 'B+'
        WHEN ROUND(
            (
                COALESCE(AVG(ea.score), 0) +
                COALESCE(AVG(
                    CASE
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) >= 60 THEN 'B'
        WHEN ROUND(
            (
                COALESCE(AVG(ea.score), 0) +
                COALESCE(AVG(
                    CASE
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) >= 50 THEN 'C+'
        WHEN ROUND(
            (
                COALESCE(AVG(ea.score), 0) +
                COALESCE(AVG(
                    CASE
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) >= 40 THEN 'C'
        WHEN ROUND(
            (
                COALESCE(AVG(ea.score), 0) +
                COALESCE(AVG(
                    CASE
                        WHEN pr.grade = 'A+' THEN 95
                        WHEN pr.grade = 'A' THEN 85
                        WHEN pr.grade = 'B+' THEN 75
                        WHEN pr.grade = 'B' THEN 65
                        WHEN pr.grade = 'C+' THEN 55
                        WHEN pr.grade = 'C' THEN 45
                        WHEN pr.grade = 'D' THEN 35
                        ELSE 0
                    END
                ), 0) +
                COALESCE(AVG(
                    CASE
                        WHEN asub.marks_obtained IS NOT NULL AND a.total_marks > 0
                        THEN (asub.marks_obtained * 100.0 / a.total_marks)
                        ELSE 0
                    END
                ), 0)
            ) * 100.0 / 300, 2
        ) >= 33 THEN 'D'
        ELSE 'F'
    END as final_grade

FROM students s
    LEFT JOIN student_subjects ss ON s.id = ss.student_id
    LEFT JOIN subjects sub ON ss.subject_id = sub.id
    LEFT JOIN exam_attempts ea ON (s.id = ea.user_id AND ea.status = 'completed')
    LEFT JOIN exams e ON ea.exam_id = e.exam_id
    LEFT JOIN practical_records pr ON s.id = pr.student_id
    LEFT JOIN practicals p ON pr.practical_id = p.id AND p.subject_id = sub.id
    LEFT JOIN assignment_submissions asub ON s.id = asub.student_id
    LEFT JOIN assignments a ON (
        asub.assignment_id = a.id
        AND a.subject_id = sub.id
        AND asub.status = 'graded'
    )

WHERE s.is_active = TRUE
    -- Add specific student filter here: AND s.student_id = 'STU001'

GROUP BY s.id, sub.id
ORDER BY sub.name;

-- =====================================================
-- ADDITIONAL UTILITY QUERIES
-- =====================================================

-- Query 3.1: Simple Student-Teacher-Room Mapping (Simplified version)
-- For cases where complex joins might not work due to missing tables
SELECT DISTINCT
    s.student_id as roll_number,
    s.name as student_name,
    s.class,
    s.section,
    s.trade,
    s.session,
    s.room_number,

    -- Class Incharge (if available)
    COALESCE(ci_user.name, 'Not Assigned') as class_incharge,

    -- Subjects (from student_subjects if available)
    GROUP_CONCAT(DISTINCT sub.name ORDER BY sub.name SEPARATOR ', ') as subjects,

    -- Subject Teachers (if available)
    GROUP_CONCAT(DISTINCT st_user.name ORDER BY st_user.name SEPARATOR ', ') as subject_teachers

FROM students s
    LEFT JOIN classes c ON s.class = c.grade
    LEFT JOIN class_incharge ci ON c.id = ci.class_id
    LEFT JOIN users ci_user ON ci.teacher_id = ci_user.id
    LEFT JOIN student_subjects ss ON s.id = ss.student_id
    LEFT JOIN subjects sub ON ss.subject_id = sub.id
    LEFT JOIN teacher_subjects ts ON sub.id = ts.subject_id
    LEFT JOIN users st_user ON ts.teacher_id = st_user.id AND st_user.role = 'teacher'

WHERE s.is_active = TRUE

GROUP BY s.id
ORDER BY s.class, s.section, s.name;

-- Query 3.2: Basic Student Marks Summary (Simplified)
-- Using only exam_attempts for basic marks calculation
SELECT
    s.student_id as roll_number,
    s.name as student_name,
    s.class,
    s.section,
    s.trade,
    s.session,

    -- Basic exam performance
    COUNT(DISTINCT ea.id) as total_exams_taken,
    ROUND(AVG(ea.score), 2) as average_score,
    ROUND(MAX(ea.score), 2) as highest_score,
    ROUND(MIN(ea.score), 2) as lowest_score,

    -- Simple grade based on average
    CASE
        WHEN AVG(ea.score) >= 90 THEN 'A+'
        WHEN AVG(ea.score) >= 80 THEN 'A'
        WHEN AVG(ea.score) >= 70 THEN 'B+'
        WHEN AVG(ea.score) >= 60 THEN 'B'
        WHEN AVG(ea.score) >= 50 THEN 'C+'
        WHEN AVG(ea.score) >= 40 THEN 'C'
        WHEN AVG(ea.score) >= 33 THEN 'D'
        ELSE 'F'
    END as overall_grade

FROM students s
    LEFT JOIN exam_attempts ea ON s.id = ea.user_id AND ea.status = 'completed'

WHERE s.is_active = TRUE

GROUP BY s.id
HAVING total_exams_taken > 0
ORDER BY s.class, s.section, average_score DESC;

-- Query 3.3: Teacher Workload Summary
SELECT
    u.id as teacher_id,
    u.name as teacher_name,
    u.email as teacher_email,

    -- Subject assignments
    COUNT(DISTINCT ts.subject_id) as subjects_assigned,
    GROUP_CONCAT(DISTINCT sub.name ORDER BY sub.name SEPARATOR ', ') as subject_list,

    -- Class assignments
    COUNT(DISTINCT tc.classroom_id) as classes_assigned,

    -- Student count
    COUNT(DISTINCT s.id) as total_students,

    -- Class incharge status
    CASE WHEN ci.teacher_id IS NOT NULL THEN 'Yes' ELSE 'No' END as is_class_incharge

FROM users u
    LEFT JOIN teacher_subjects ts ON u.id = ts.teacher_id
    LEFT JOIN subjects sub ON ts.subject_id = sub.id
    LEFT JOIN teacher_classes tc ON u.id = tc.teacher_id
    LEFT JOIN classrooms cr ON tc.classroom_id = cr.id
    LEFT JOIN students s ON (
        s.class = (SELECT c.grade FROM classes c WHERE c.id = cr.class_id)
        AND s.section = cr.section
        AND s.session = cr.session
        AND s.is_active = TRUE
    )
    LEFT JOIN class_incharge ci ON u.id = ci.teacher_id

WHERE u.role = 'teacher' AND u.is_active = TRUE

GROUP BY u.id
ORDER BY total_students DESC, subjects_assigned DESC;

-- =====================================================
-- SUMMARY AND NOTES
-- =====================================================

/*
INSIGHTS QUERIES SUMMARY:

REQUIREMENT 1: Student Incharge, Subject Teachers, Subjects, Class, Trade, Section, Room Details
- Query 1.1: Complete Student-Teacher-Subject-Class Mapping (Comprehensive)
- Query 1.2: Class-wise Summary with Incharge and Room Details
- Query 1.3: Subject-wise Teacher Assignment Details
- Query 3.1: Simple Student-Teacher-Room Mapping (Simplified)

REQUIREMENT 2: Student Final Marks - Theory, CCE, Practical, Total, Percentage, Grade, Session
- Query 2.1: Comprehensive Student Marks Report (Full calculation)
- Query 2.2: Class-wise Performance Summary
- Query 2.3: Individual Student Report Card
- Query 3.2: Basic Student Marks Summary (Simplified)

ADDITIONAL UTILITIES:
- Query 3.3: Teacher Workload Summary

NOTES:
1. These queries are designed to work with the existing database structure
2. Some queries may return empty results if certain tables (like student_subjects, teacher_subjects) are not populated
3. The marks calculation assumes:
   - Theory marks from exam_attempts table
   - Practical marks from practical_records table (converted from grades)
   - CCE marks from assignment_submissions table
4. Grade calculation follows standard percentage ranges:
   - A+: 90-100%, A: 80-89%, B+: 70-79%, B: 60-69%
   - C+: 50-59%, C: 40-49%, D: 33-39%, F: Below 33%
5. For production use, you may need to:
   - Create dedicated student_marks table for better performance
   - Add proper indexes on frequently queried columns
   - Implement proper academic session management
   - Add validation for data consistency

USAGE EXAMPLES:
- To get data for a specific student: Add "AND s.student_id = 'STU001'" to WHERE clause
- To get data for a specific class: Add "AND s.class = '10' AND s.section = 'A'" to WHERE clause
- To get data for a specific session: Add "AND s.session = '2024-25'" to WHERE clause
- To get data for a specific subject: Add "AND sub.name = 'Mathematics'" to WHERE clause
*/
