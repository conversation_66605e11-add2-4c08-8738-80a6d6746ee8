/**
 * Circular Progress Indicators
 * Reusable CSS components for displaying percentage values in circular format
 */

/* Base circular progress container */
.circular-progress {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: conic-gradient(
    var(--progress-color, #3b82f6) calc(var(--progress, 0) * 1%),
    #e5e7eb calc(var(--progress, 0) * 1%)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

/* Inner circle to create the ring effect */
.circular-progress::before {
  content: '';
  position: absolute;
  width: 75%;
  height: 75%;
  background: white;
  border-radius: 50%;
  z-index: 1;
}

/* Progress text */
.circular-progress .progress-text {
  position: relative;
  z-index: 2;
  font-size: 11px;
  font-weight: 600;
  color: #374151;
}

/* Size variations */
.circular-progress.small {
  width: 40px;
  height: 40px;
  font-size: 10px;
}

.circular-progress.medium {
  width: 60px;
  height: 60px;
  font-size: 12px;
}

.circular-progress.large {
  width: 80px;
  height: 80px;
  font-size: 14px;
}

/* Color variations */
.circular-progress.success {
  --progress-color: #10b981;
}

.circular-progress.warning {
  --progress-color: #f59e0b;
}

.circular-progress.danger {
  --progress-color: #ef4444;
}

.circular-progress.info {
  --progress-color: #3b82f6;
}

.circular-progress.primary {
  --progress-color: #6366f1;
}

/* Score card container */
.score-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.score-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.score-card .score-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.score-card .score-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.score-card .score-subtitle {
  font-size: 12px;
  color: #6b7280;
  margin: 4px 0 0 0;
}

.score-card .score-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.score-card .score-details {
  flex: 1;
}

.score-card .score-value {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  margin: 0;
}

.score-card .score-description {
  font-size: 12px;
  color: #6b7280;
  margin: 4px 0 0 0;
}

/* Score cards grid */
.score-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin: 20px 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .score-cards-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .score-card {
    padding: 16px;
  }
  
  .circular-progress.large {
    width: 60px;
    height: 60px;
    font-size: 12px;
  }
  
  .circular-progress.medium {
    width: 50px;
    height: 50px;
    font-size: 11px;
  }
}

/* Animation for progress loading */
@keyframes progressLoad {
  from {
    --progress: 0;
  }
  to {
    --progress: var(--final-progress);
  }
}

.circular-progress.animate {
  animation: progressLoad 1.5s ease-out forwards;
}

/* Table circular progress (smaller for table cells) */
.table-circular-progress {
  width: 36px;
  height: 36px;
  font-size: 10px;
  margin: 0 auto;
}

.table-circular-progress .progress-text {
  font-size: 9px;
}

/* Status indicators based on percentage */
.circular-progress[style*="--progress: 0"], 
.circular-progress[style*="--progress: 1"], 
.circular-progress[style*="--progress: 2"], 
.circular-progress[style*="--progress: 3"], 
.circular-progress[style*="--progress: 4"] {
  --progress-color: #ef4444; /* Red for very low */
}

.circular-progress[style*="--progress: 5"], 
.circular-progress[style*="--progress: 6"], 
.circular-progress[style*="--progress: 7"], 
.circular-progress[style*="--progress: 8"], 
.circular-progress[style*="--progress: 9"] {
  --progress-color: #f59e0b; /* Orange for low */
}

/* Utility classes for quick styling */
.text-center { text-align: center; }
.font-semibold { font-weight: 600; }
.text-sm { font-size: 14px; }
.text-xs { font-size: 12px; }
.text-gray-600 { color: #6b7280; }
.text-gray-700 { color: #374151; }
.text-gray-900 { color: #111827; }
