<!-- Teacher Details Page -->
<div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-semibold text-gray-900">Teacher Details</h1>
                        <p class="mt-1 text-sm text-gray-600">Comprehensive faculty profile management and information</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button id="refreshData" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-sync-alt mr-2"></i>
                            Refresh
                        </button>
                        <button id="exportData" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gray-600 hover:bg-gray-700">
                            <i class="fas fa-download mr-2"></i>
                            Export
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-users text-gray-600"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Teachers</dt>
                                <dd class="text-lg font-medium text-gray-900"><%= teachers.length %></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-user-check text-green-600"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Active Teachers</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <%= teachers.filter(t => t.is_active).length %>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-graduation-cap text-blue-600"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Departments</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <%= [...new Set(teachers.map(t => t.department).filter(Boolean))].length %>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-chart-line text-purple-600"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Avg Experience</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <%= Math.round(teachers.reduce((sum, t) => sum + (t.total_experience_years || 0), 0) / teachers.length) || 0 %> years
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Search & Filter</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="searchInput" class="block text-sm font-medium text-gray-700 mb-2">Search Teachers</label>
                        <input type="text" id="searchInput" placeholder="Search by name, email, or employee ID..."
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-gray-500 focus:border-gray-500">
                    </div>
                    <div>
                        <label for="departmentFilter" class="block text-sm font-medium text-gray-700 mb-2">Department</label>
                        <select id="departmentFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-gray-500 focus:border-gray-500">
                            <option value="">All Departments</option>
                            <% [...new Set(teachers.map(t => t.department).filter(Boolean))].forEach(dept => { %>
                                <option value="<%= dept %>"><%= dept %></option>
                            <% }); %>
                        </select>
                    </div>
                    <div>
                        <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-gray-500 focus:border-gray-500">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Teachers Table -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">Faculty Directory</h3>
                    <div class="flex items-center space-x-3">
                        <div id="selectedTeachersInfo" class="text-sm text-gray-600 hidden">
                            <span id="selectedCount">0</span> teachers selected
                        </div>
                        <button id="exportSelectedBtn" class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 hidden">
                            <i class="fas fa-download mr-2"></i>
                            Export Selected
                        </button>
                        <button id="clearSelectionBtn" class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 hidden">
                            <i class="fas fa-times mr-2"></i>
                            Clear Selection
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-hidden">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <div class="flex items-center">
                                        <input type="checkbox" id="selectAllTeachers" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <label for="selectAllTeachers" class="ml-2 text-xs font-medium text-gray-500 uppercase tracking-wider">Select</label>
                                    </div>
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Experience</th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Profile</th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Rating</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="teachersTableBody" class="bg-white divide-y divide-gray-200">
                            <% if (teachers && teachers.length > 0) { %>
                                <% teachers.forEach(teacher => { %>
                                    <tr class="hover:bg-gray-50 teacher-row"
                                        data-teacher-id="<%= teacher.id %>"
                                        data-name="<%= (teacher.name || '').toLowerCase() %>"
                                        data-email="<%= (teacher.email || '').toLowerCase() %>"
                                        data-department="<%= teacher.department || '' %>"
                                        data-status="<%= teacher.is_active ? 'active' : 'inactive' %>">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="teacher-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                                   value="<%= teacher.id %>" data-teacher-name="<%= teacher.name || 'Unknown' %>">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                                        <span class="text-sm font-medium text-gray-600">
                                                            <%= (teacher.name || 'T').split(' ').map(n => n[0]).join('').toUpperCase() %>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900"><%= teacher.name || 'Unknown' %></div>
                                                    <div class="text-sm text-gray-500"><%= teacher.email || 'No email' %></div>
                                                    <% if (teacher.employee_id) { %>
                                                        <div class="text-xs text-gray-400">ID: <%= teacher.employee_id %></div>
                                                    <% } %>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900"><%= teacher.department || 'Not specified' %></div>
                                            <div class="text-sm text-gray-500"><%= teacher.designation || 'Teacher' %></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900"><%= teacher.total_experience_years || 0 %> years</div>
                                            <% if (teacher.teaching_experience_years) { %>
                                                <div class="text-sm text-gray-500"><%= teacher.teaching_experience_years %> years teaching</div>
                                            <% } %>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <%
                                                // Calculate basic profile completion based on available data
                                                let profileFields = 0;
                                                let completedFields = 0;

                                                // Basic fields to check
                                                const fieldsToCheck = ['name', 'email', 'department', 'designation', 'phone', 'employee_id'];
                                                fieldsToCheck.forEach(field => {
                                                    profileFields++;
                                                    if (teacher[field] && teacher[field].toString().trim() !== '') {
                                                        completedFields++;
                                                    }
                                                });

                                                const profileCompletion = Math.round((completedFields / profileFields) * 100);
                                                let profileColor = 'danger';
                                                if (profileCompletion >= 80) profileColor = 'success';
                                                else if (profileCompletion >= 60) profileColor = 'info';
                                                else if (profileCompletion >= 40) profileColor = 'warning';
                                            %>
                                            <div class="circular-progress table-circular-progress <%= profileColor %>" style="--progress: <%= profileCompletion %>">
                                                <span class="progress-text"><%= profileCompletion %>%</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <%
                                                // Calculate basic rating based on available data
                                                let ratingScore = 50; // Default base score

                                                // Add points for experience
                                                const experience = teacher.total_experience_years || 0;
                                                if (experience >= 10) ratingScore += 30;
                                                else if (experience >= 5) ratingScore += 20;
                                                else if (experience >= 2) ratingScore += 10;

                                                // Add points for active status
                                                if (teacher.is_active) ratingScore += 10;

                                                // Add points for having complete basic info
                                                if (teacher.department && teacher.designation) ratingScore += 10;

                                                ratingScore = Math.min(ratingScore, 100);

                                                let ratingColor = 'danger';
                                                if (ratingScore >= 80) ratingColor = 'success';
                                                else if (ratingScore >= 60) ratingColor = 'info';
                                                else if (ratingScore >= 40) ratingColor = 'warning';
                                            %>
                                            <div class="circular-progress table-circular-progress <%= ratingColor %>" style="--progress: <%= ratingScore %>">
                                                <span class="progress-text"><%= ratingScore %>%</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <% if (teacher.is_active) { %>
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                                            <% } else { %>
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>
                                            <% } %>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <% if (teacher.last_login) { %>
                                                <%= new Date(teacher.last_login).toLocaleDateString() %>
                                            <% } else { %>
                                                Never
                                            <% } %>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex items-center space-x-2">
                                                <button class="viewTeacherBtn text-gray-600 hover:text-gray-900 p-1 rounded"
                                                        data-teacher-id="<%= teacher.id %>"
                                                        title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="editTeacherBtn text-gray-600 hover:text-gray-900 p-1 rounded"
                                                        data-teacher-id="<%= teacher.id %>"
                                                        title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="generateCVBtn text-gray-600 hover:text-gray-900 p-1 rounded"
                                                        data-teacher-id="<%= teacher.id %>"
                                                        data-teacher-name="<%= teacher.name %>"
                                                        title="Generate CV">
                                                    <i class="fas fa-file-pdf"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <% }); %>
                            <% } else { %>
                                <tr>
                                    <td colspan="9" class="px-6 py-12 text-center">
                                        <div class="text-gray-500">
                                            <i class="fas fa-users text-4xl mb-4"></i>
                                            <h3 class="text-lg font-medium">No teachers found</h3>
                                            <p class="text-sm">No teacher records are available at this time.</p>
                                        </div>
                                    </td>
                                </tr>
                            <% } %>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Teacher Details Modal -->
<div id="teacherModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
            <!-- Modal Header -->
            <div class="bg-white px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 id="modalTitle" class="text-lg font-semibold text-gray-900">Teacher Details</h3>
                        <p id="modalSubtitle" class="text-sm text-gray-500 mt-1">Loading teacher information...</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button id="downloadCVBtn" class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-file-pdf mr-2"></i>Download CV
                        </button>
                        <button id="closeModalBtn" class="text-gray-400 hover:text-gray-600 p-1">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Teacher General Information (Always Visible) -->
            <div id="teacherGeneralInfo" class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                <div class="flex items-center space-x-4">
                    <div class="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center">
                        <span id="teacherInitials" class="text-xl font-semibold text-gray-600">--</span>
                    </div>
                    <div class="flex-1">
                        <h4 id="teacherName" class="text-lg font-semibold text-gray-900">Teacher Name</h4>
                        <p id="teacherDesignation" class="text-sm text-gray-600">Designation • Department</p>
                        <p id="teacherContact" class="text-sm text-gray-500">Email • Phone</p>
                    </div>
                    <div class="text-right">
                        <div class="text-sm text-gray-500">
                            <span id="employeeId">Employee ID</span>
                        </div>
                        <div class="text-sm text-gray-500 mt-1">
                            <span id="experienceInfo">0 years experience</span>
                        </div>
                        <div class="mt-2">
                            <span id="statusBadge" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Score Cards -->
            <div id="teacherScoreCards" class="px-6 py-4 bg-white border-b border-gray-200">
                <div class="score-cards-grid">
                    <!-- Profile Completion Card -->
                    <div class="score-card">
                        <div class="score-header">
                            <div>
                                <h5 class="score-title">Profile Completion</h5>
                                <p class="score-subtitle">Data completeness</p>
                            </div>
                        </div>
                        <div class="score-content">
                            <div class="score-details">
                                <h3 id="profileCompletionValue" class="score-value">0%</h3>
                                <p id="profileCompletionDescription" class="score-description">Complete your profile</p>
                            </div>
                            <div id="profileCompletionCircle" class="circular-progress medium" style="--progress: 0">
                                <span class="progress-text">0%</span>
                            </div>
                        </div>
                    </div>

                    <!-- Rating Score Card -->
                    <div class="score-card">
                        <div class="score-header">
                            <div>
                                <h5 class="score-title">Performance Rating</h5>
                                <p class="score-subtitle">Overall assessment</p>
                            </div>
                        </div>
                        <div class="score-content">
                            <div class="score-details">
                                <h3 id="ratingScoreValue" class="score-value">0%</h3>
                                <p id="ratingScoreDescription" class="score-description">Performance evaluation</p>
                            </div>
                            <div id="ratingScoreCircle" class="circular-progress medium" style="--progress: 0">
                                <span class="progress-text">0%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Navigation -->
            <div class="border-b border-gray-200">
                <nav class="flex space-x-8 px-6" id="modalTabs">
                    <button class="tab-btn active py-4 px-1 border-b-2 border-gray-600 font-medium text-sm text-gray-900" data-tab="personal">
                        Personal Information
                    </button>
                    <button class="tab-btn py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" data-tab="education">
                        Education
                    </button>
                    <button class="tab-btn py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" data-tab="experience">
                        Experience
                    </button>
                    <button class="tab-btn py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" data-tab="skills">
                        Skills & Certifications
                    </button>
                    <button class="tab-btn py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" data-tab="achievements">
                        Achievements
                    </button>
                </nav>
            </div>

            <!-- Tab Content -->
            <div class="overflow-y-auto max-h-96">
                <!-- Loading State -->
                <div id="modalLoading" class="flex items-center justify-center py-12">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-600 mr-3"></div>
                    <span class="text-sm text-gray-600">Loading teacher profile...</span>
                </div>

                <!-- Personal Information Tab -->
                <div id="personalTab" class="tab-content p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-sm font-semibold text-gray-900 mb-3">Basic Information</h4>
                            <div class="space-y-3 text-sm">
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Date of Birth:</span>
                                    <span id="modalDateOfBirth" class="text-gray-600">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Gender:</span>
                                    <span id="modalGender" class="text-gray-600">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Username:</span>
                                    <span id="modalUsername" class="text-gray-600">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Joining Date:</span>
                                    <span id="modalJoiningDate" class="text-gray-600">-</span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-sm font-semibold text-gray-900 mb-3">Contact Information</h4>
                            <div class="space-y-3 text-sm">
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Emergency Contact:</span>
                                    <span id="modalEmergencyContact" class="text-gray-600">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Address:</span>
                                    <span id="modalAddress" class="text-gray-600">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Office Location:</span>
                                    <span id="modalOfficeLocation" class="text-gray-600">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Languages:</span>
                                    <span id="modalLanguages" class="text-gray-600">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Education Tab -->
                <div id="educationTab" class="tab-content hidden p-6">
                    <div id="modalEducationContent">
                        <p class="text-sm text-gray-500">No education data available</p>
                    </div>
                </div>

                <!-- Experience Tab -->
                <div id="experienceTab" class="tab-content hidden p-6">
                    <div id="modalExperienceContent">
                        <p class="text-sm text-gray-500">No experience data available</p>
                    </div>
                </div>

                <!-- Skills & Certifications Tab -->
                <div id="skillsTab" class="tab-content hidden p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-sm font-semibold text-gray-900 mb-3">Skills</h4>
                            <div id="modalSkillsContent">
                                <p class="text-sm text-gray-500">No skills data available</p>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-sm font-semibold text-gray-900 mb-3">Certifications</h4>
                            <div id="modalCertificationsContent">
                                <p class="text-sm text-gray-500">No certifications available</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Achievements Tab -->
                <div id="achievementsTab" class="tab-content hidden p-6">
                    <div id="modalAchievementsContent">
                        <p class="text-sm text-gray-500">No achievements recorded</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Teacher Details -->
<script src="/js/teacher-details.js?v=<%= Date.now() %>"></script>
