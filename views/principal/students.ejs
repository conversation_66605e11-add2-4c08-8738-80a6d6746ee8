<!-- Principal Student Data Overview Page -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
  <!-- Header -->
  <div class="bg-gradient-to-r from-principal-primary to-principal-secondary text-white p-6">
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold">Student Data Overview</h1>
        <p class="text-principal-light mt-1">Comprehensive student information and analytics</p>
      </div>
      <div class="flex space-x-3">
        <button id="exportSelectedBtn" class="bg-white bg-opacity-20 text-white px-4 py-2 rounded-lg font-medium hover:bg-opacity-30 transition-colors hidden">
          <i class="fas fa-download mr-2"></i>Export Selected (<span id="selectedCount">0</span>)
        </button>
        <button id="exportAllBtn" class="bg-white bg-opacity-20 text-white px-4 py-2 rounded-lg font-medium hover:bg-opacity-30 transition-colors">
          <i class="fas fa-download mr-2"></i>Export Data
        </button>
        <div class="bg-white bg-opacity-20 px-4 py-2 rounded-lg">
          <i class="fas fa-eye mr-2"></i>
          <span class="font-medium">Read-Only View</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Flash Messages -->
  <% if (flashSuccess) { %>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 m-4 rounded">
      <i class="fas fa-check-circle mr-2"></i><%= flashSuccess %>
    </div>
  <% } %>
  <% if (flashError) { %>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 m-4 rounded">
      <i class="fas fa-exclamation-circle mr-2"></i><%= flashError %>
    </div>
  <% } %>
  <% if (flashInfo) { %>
    <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 m-4 rounded">
      <i class="fas fa-info-circle mr-2"></i><%= flashInfo %>
    </div>
  <% } %>

  <!-- Filters Section -->
  <div class="p-6 bg-principal-light border-b">
    <form id="filterForm" method="GET" class="space-y-4">
      <!-- Quick Filters -->
      <div class="space-y-3">
        <h4 class="text-sm font-medium text-principal-dark">Quick Filters</h4>
        <div class="flex flex-wrap gap-2">
          <button type="button" class="quick-filter px-4 py-2 text-sm rounded-full border-2 border-gray-300 bg-white text-gray-700 hover:bg-gray-50 transition-all duration-200 font-medium" data-filter="all">
            <i class="fas fa-users mr-2"></i>All Students (<%= pagination.totalStudents %>)
          </button>
          <button type="button" class="quick-filter px-4 py-2 text-sm rounded-full border-2 border-blue-300 bg-blue-50 text-blue-700 hover:bg-blue-100 transition-all duration-200 font-medium" data-filter="male">
            <i class="fas fa-mars mr-2"></i>Male Students
          </button>
          <button type="button" class="quick-filter px-4 py-2 text-sm rounded-full border-2 border-pink-300 bg-pink-50 text-pink-700 hover:bg-pink-100 transition-all duration-200 font-medium" data-filter="female">
            <i class="fas fa-venus mr-2"></i>Female Students
          </button>
          <button type="button" class="quick-filter px-4 py-2 text-sm rounded-full border-2 border-orange-300 bg-orange-50 text-orange-700 hover:bg-orange-100 transition-all duration-200 font-medium" data-filter="bpl">
            <i class="fas fa-hand-holding-heart mr-2"></i>BPL Students
          </button>
          <button type="button" class="quick-filter px-4 py-2 text-sm rounded-full border-2 border-purple-300 bg-purple-50 text-purple-700 hover:bg-purple-100 transition-all duration-200 font-medium" data-filter="disability">
            <i class="fas fa-wheelchair mr-2"></i>Students with Disability
          </button>
          <button type="button" class="quick-filter px-4 py-2 text-sm rounded-full border-2 border-green-300 bg-green-50 text-green-700 hover:bg-green-100 transition-all duration-200 font-medium" data-filter="science">
            <i class="fas fa-flask mr-2"></i>Science Stream
          </button>
          <button type="button" class="quick-filter px-4 py-2 text-sm rounded-full border-2 border-indigo-300 bg-indigo-50 text-indigo-700 hover:bg-indigo-100 transition-all duration-200 font-medium" data-filter="commerce">
            <i class="fas fa-chart-line mr-2"></i>Commerce Stream
          </button>

        </div>
      </div>

      <!-- Advanced Filters Toggle -->
      <div class="flex items-center justify-between">
        <h4 class="text-sm font-medium text-principal-dark">Advanced Filters</h4>
        <button type="button" id="toggleFilters" class="flex items-center text-principal-primary hover:text-principal-hover transition-colors">
          <span class="mr-2">Toggle Filters</span>
          <i class="fas fa-chevron-down transition-transform duration-200" id="filterToggleIcon"></i>
        </button>
      </div>

      <!-- Advanced Filters -->
      <div id="advancedFilters" class="hidden space-y-4">
        <div class="border-t border-gray-200 pt-4">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
        <!-- Search -->
        <div class="lg:col-span-2">
          <label class="block text-sm font-medium text-principal-dark mb-1">Search</label>
          <input type="text" name="search" id="searchInput" value="<%= filters.search %>"
                 placeholder="Name, ID, Father's Name, Roll No..."
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-principal-primary">
        </div>

        <!-- Class -->
        <div>
          <label class="block text-sm font-medium text-principal-dark mb-1">Class</label>
          <select name="class" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-principal-primary">
            <option value="">All Classes</option>
            <% classes.forEach(cls => { %>
              <option value="<%= cls %>" <%= filters.class === cls ? 'selected' : '' %>><%= cls %></option>
            <% }); %>
          </select>
        </div>

        <!-- Section -->
        <div>
          <label class="block text-sm font-medium text-principal-dark mb-1">Section</label>
          <select name="section" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-principal-primary">
            <option value="">All Sections</option>
            <% sections.forEach(section => { %>
              <option value="<%= section %>" <%= filters.section === section ? 'selected' : '' %>><%= section %></option>
            <% }); %>
          </select>
        </div>

        <!-- Session -->
        <div>
          <label class="block text-sm font-medium text-principal-dark mb-1">Session</label>
          <select name="session" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-principal-primary">
            <option value="">All Sessions</option>
            <% sessions.forEach(session => { %>
              <option value="<%= session %>" <%= filters.session === session ? 'selected' : '' %>><%= session %></option>
            <% }); %>
          </select>
        </div>

        <!-- Gender -->
        <div>
          <label class="block text-sm font-medium text-principal-dark mb-1">Gender</label>
          <select name="gender" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-principal-primary">
            <option value="">All Genders</option>
            <option value="Male" <%= filters.gender === 'Male' ? 'selected' : '' %>>Male</option>
            <option value="Female" <%= filters.gender === 'Female' ? 'selected' : '' %>>Female</option>
            <option value="Other" <%= filters.gender === 'Other' ? 'selected' : '' %>>Other</option>
          </select>
        </div>

        <!-- Stream -->
        <div>
          <label class="block text-sm font-medium text-principal-dark mb-1">Stream</label>
          <select name="stream" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-principal-primary">
            <option value="">All Streams</option>
            <% streams.forEach(stream => { %>
              <option value="<%= stream %>" <%= filters.stream === stream ? 'selected' : '' %>><%= stream %></option>
            <% }); %>
          </select>
        </div>

        <!-- BPL -->
        <div>
          <label class="block text-sm font-medium text-principal-dark mb-1">BPL Status</label>
          <select name="bpl" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-principal-primary">
            <option value="">All</option>
            <option value="Yes" <%= filters.bpl === 'Yes' ? 'selected' : '' %>>BPL</option>
            <option value="No" <%= filters.bpl === 'No' ? 'selected' : '' %>>Non-BPL</option>
          </select>
        </div>

        <!-- Disability -->
        <div>
          <label class="block text-sm font-medium text-principal-dark mb-1">Disability</label>
          <select name="disability" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-principal-primary">
            <option value="">All</option>
            <option value="Yes" <%= filters.disability === 'Yes' ? 'selected' : '' %>>With Disability</option>
            <option value="No" <%= filters.disability === 'No' ? 'selected' : '' %>>Without Disability</option>
          </select>
        </div>

        <!-- Records per page -->
        <div>
          <label class="block text-sm font-medium text-principal-dark mb-1">Per Page</label>
          <select name="limit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-principal-primary">
            <option value="25" <%= pagination.limit === 25 ? 'selected' : '' %>>25</option>
            <option value="50" <%= pagination.limit === 50 ? 'selected' : '' %>>50</option>
            <option value="100" <%= pagination.limit === 100 ? 'selected' : '' %>>100</option>
            <option value="200" <%= pagination.limit === 200 ? 'selected' : '' %>>200</option>
          </select>
        </div>
      </div>

      <!-- Filter Actions -->
      <div class="flex space-x-3">
        <button type="submit" class="bg-principal-primary text-white px-4 py-2 rounded-md hover:bg-principal-hover transition-colors">
          <i class="fas fa-filter mr-2"></i>Apply Filters
        </button>
            <button type="button" id="clearFilters" class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 transition-colors">
              <i class="fas fa-times mr-2"></i>Clear All
            </button>
          </div>
        </div>
      </div>
    </form>
  </div>

  <!-- Results Summary -->
  <div class="px-6 py-3 bg-principal-light border-b">
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
      <div class="text-sm text-principal-dark">
        Showing <%= (pagination.currentPage - 1) * pagination.limit + 1 %> to
        <%= Math.min(pagination.currentPage * pagination.limit, pagination.totalStudents) %>
        of <%= pagination.totalStudents %> students
      </div>
      <div class="flex items-center gap-4">
        <div class="text-sm text-principal-dark">
          Page <%= pagination.currentPage %> of <%= pagination.totalPages %>
        </div>
        <div class="text-xs text-principal-silver hidden sm:block">
          <i class="fas fa-arrows-alt-h mr-1"></i>Scroll horizontally to view all columns
        </div>
      </div>
    </div>
  </div>

  <!-- Data Table -->
  <div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-principal-light">
        <tr>
          <th class="px-6 py-3 text-left text-xs font-medium text-principal-dark uppercase tracking-wider">
            <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-principal-primary focus:ring-principal-primary">
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-principal-dark uppercase tracking-wider">S.No</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-principal-dark uppercase tracking-wider">Student ID</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-principal-dark uppercase tracking-wider">Name</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-principal-dark uppercase tracking-wider">Class</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-principal-dark uppercase tracking-wider">Section</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-principal-dark uppercase tracking-wider">Gender</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-principal-dark uppercase tracking-wider">Roll No</th>
          <th class="px-6 py-3 text-center text-xs font-medium text-principal-dark uppercase tracking-wider">Profile</th>
          <th class="px-6 py-3 text-center text-xs font-medium text-principal-dark uppercase tracking-wider">Rating</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-principal-dark uppercase tracking-wider">Contact</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-principal-dark uppercase tracking-wider">Actions</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% if (students && students.length > 0) { %>
          <% students.forEach((student, index) => { %>
            <tr class="hover:bg-principal-light transition-colors student-row" data-student-id="<%= student.student_id || index %>" data-student-db-id="<%= student.id %>">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-principal-dark">
                <input type="checkbox" class="student-checkbox rounded border-gray-300 text-principal-primary focus:ring-principal-primary"
                       value="<%= student.id %>" data-student-data='<%= JSON.stringify(student) %>'>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-principal-dark"><%= student.sno || ((pagination.currentPage - 1) * pagination.limit + index + 1) %></td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-principal-primary"><%= student.student_id || '-' %></td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-principal-dark"><%= student.name || '-' %></td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-principal-dark"><%= student.class || '-' %></td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-principal-dark"><%= student.section || '-' %></td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-principal-dark">
                <span class="px-2 py-1 text-xs rounded-full <%= student.gender === 'Male' ? 'bg-blue-100 text-blue-800' : student.gender === 'Female' ? 'bg-pink-100 text-pink-800' : 'bg-gray-100 text-gray-800' %>">
                  <%= student.gender || '-' %>
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-principal-dark"><%= student.roll_no || '-' %></td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <%
                  // Calculate basic profile completion for student
                  let studentProfileFields = 0;
                  let studentCompletedFields = 0;

                  // Basic fields to check for students
                  const studentFieldsToCheck = ['name', 'father_name', 'mother_name', 'dob', 'gender', 'student_id', 'class', 'section', 'contact_no'];
                  studentFieldsToCheck.forEach(field => {
                    studentProfileFields++;
                    if (student[field] && student[field].toString().trim() !== '') {
                      studentCompletedFields++;
                    }
                  });

                  const studentProfileCompletion = Math.round((studentCompletedFields / studentProfileFields) * 100);
                  let studentProfileColor = 'danger';
                  if (studentProfileCompletion >= 80) studentProfileColor = 'success';
                  else if (studentProfileCompletion >= 60) studentProfileColor = 'info';
                  else if (studentProfileCompletion >= 40) studentProfileColor = 'warning';
                %>
                <div class="circular-progress table-circular-progress <%= studentProfileColor %>" style="--progress: <%= studentProfileCompletion %>">
                  <span class="progress-text"><%= studentProfileCompletion %>%</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <%
                  // Calculate basic rating for student
                  let studentRatingScore = 40; // Default base score

                  // Add points for complete basic information
                  if (student.name && student.father_name && student.mother_name) studentRatingScore += 20;

                  // Add points for academic information
                  if (student.class && student.section && student.roll_no) studentRatingScore += 20;

                  // Add points for contact information
                  if (student.contact_no && student.contact_no.toString().trim() !== '') studentRatingScore += 10;

                  // Add points for address information
                  if (student.cur_address && student.pin_code) studentRatingScore += 10;

                  studentRatingScore = Math.min(studentRatingScore, 100);

                  let studentRatingColor = 'danger';
                  if (studentRatingScore >= 80) studentRatingColor = 'success';
                  else if (studentRatingScore >= 60) studentRatingColor = 'info';
                  else if (studentRatingScore >= 40) studentRatingColor = 'warning';
                %>
                <div class="circular-progress table-circular-progress <%= studentRatingColor %>" style="--progress: <%= studentRatingScore %>">
                  <span class="progress-text"><%= studentRatingScore %>%</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-principal-dark"><%= student.contact_no || '-' %></td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-principal-dark">
                <div class="flex space-x-2">
                  <button id="viewBtn-<%= student.id %>" class="view-student-btn text-blue-600 hover:text-blue-900 p-1 rounded" data-student-id="<%= student.id %>" title="View Details">
                    <i class="fas fa-eye"></i>
                  </button>
                </div>
              </td>
            </tr>
            <!-- Expandable Details Row -->
            <tr id="details-<%= student.student_id || index %>" class="hidden bg-gray-50">
              <td colspan="12" class="px-6 py-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <!-- Personal Information -->
                  <div class="bg-white p-4 rounded-lg shadow-sm">
                    <h4 class="font-semibold text-principal-dark mb-3 flex items-center">
                      <i class="fas fa-user mr-2 text-principal-primary"></i>Personal Information
                    </h4>
                    <div class="space-y-2 text-sm">
                      <div><span class="font-medium">UdiseCode:</span> <%= student.udise_code || '-' %></div>
                      <div><span class="font-medium">Father's Name:</span> <%= student.father_name || '-' %></div>
                      <div><span class="font-medium">Mother's Name:</span> <%= student.mother_name || '-' %></div>
                      <div><span class="font-medium">DOB:</span> <%= student.dob ? new Date(student.dob).toLocaleDateString('en-IN') : '-' %></div>
                      <div><span class="font-medium">Religion:</span> <%= student.religion_name || '-' %></div>
                      <div><span class="font-medium">Caste Category:</span> <%= student.caste_category_name || '-' %></div>
                    </div>
                  </div>

                  <!-- Academic Information -->
                  <div class="bg-white p-4 rounded-lg shadow-sm">
                    <h4 class="font-semibold text-principal-dark mb-3 flex items-center">
                      <i class="fas fa-graduation-cap mr-2 text-principal-primary"></i>Academic Information
                    </h4>
                    <div class="space-y-2 text-sm">
                      <div><span class="font-medium">Stream:</span> <%= student.stream || '-' %></div>
                      <div><span class="font-medium">Trade:</span> <%= student.trade || '-' %></div>
                      <div><span class="font-medium">Medium:</span> <%= student.medium_name || '-' %></div>
                      <div><span class="font-medium">Admission No:</span> <%= student.admission_no || '-' %></div>
                      <div><span class="font-medium">Admission Date:</span> <%= student.admission_date ? new Date(student.admission_date).toLocaleDateString('en-IN') : '-' %></div>
                    </div>
                  </div>

                  <!-- Address & Contact -->
                  <div class="bg-white p-4 rounded-lg shadow-sm">
                    <h4 class="font-semibold text-principal-dark mb-3 flex items-center">
                      <i class="fas fa-map-marker-alt mr-2 text-principal-primary"></i>Address & Contact
                    </h4>
                    <div class="space-y-2 text-sm">
                      <div><span class="font-medium">Address:</span> <%= student.cur_address || '-' %></div>
                      <div><span class="font-medium">Village/Ward:</span> <%= student.village_ward || '-' %></div>
                      <div><span class="font-medium">Gram Panchayat:</span> <%= student.gram_panchayat || '-' %></div>
                      <div><span class="font-medium">Pin Code:</span> <%= student.pin_code || '-' %></div>
                      <div><span class="font-medium">District:</span> <%= student.district_name || '-' %></div>
                      <div><span class="font-medium">State:</span> <%= student.state_name || '-' %></div>
                    </div>
                  </div>

                  <!-- Additional Information -->
                  <div class="bg-white p-4 rounded-lg shadow-sm">
                    <h4 class="font-semibold text-principal-dark mb-3 flex items-center">
                      <i class="fas fa-info-circle mr-2 text-principal-primary"></i>Additional Information
                    </h4>
                    <div class="space-y-2 text-sm">
                      <div><span class="font-medium">BPL Status:</span>
                        <span class="px-2 py-1 text-xs rounded-full <%= student.bpl === 'Yes' ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800' %>">
                          <%= student.bpl || 'No' %>
                        </span>
                      </div>
                      <div><span class="font-medium">Disability:</span>
                        <span class="px-2 py-1 text-xs rounded-full <%= student.disability === 'Yes' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800' %>">
                          <%= student.disability || 'No' %>
                        </span>
                      </div>
                      <div><span class="font-medium">Height:</span> <%= student.height || '-' %></div>
                      <div><span class="font-medium">Weight:</span> <%= student.weight || '-' %></div>
                    </div>
                  </div>

                  <!-- Bank Information -->
                  <div class="bg-white p-4 rounded-lg shadow-sm">
                    <h4 class="font-semibold text-principal-dark mb-3 flex items-center">
                      <i class="fas fa-university mr-2 text-principal-primary"></i>Bank Information
                    </h4>
                    <div class="space-y-2 text-sm">
                      <div><span class="font-medium">Bank Name:</span> <%= student.bank_name || '-' %></div>
                      <div><span class="font-medium">IFSC Code:</span> <%= student.ifsc_code || '-' %></div>
                      <div><span class="font-medium">Account Holder:</span> <%= student.account_holder || '-' %></div>
                      <div><span class="font-medium">Account Holder Name:</span> <%= student.account_holder_name || '-' %></div>
                      <div><span class="font-medium">Account Holder Code:</span> <%= student.account_holder_code || '-' %></div>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
          <% }); %>
        <% } else { %>
          <tr>
            <td colspan="12" class="px-6 py-12 text-center text-principal-silver">
              <div class="flex flex-col items-center">
                <i class="fas fa-users text-4xl text-principal-light mb-4"></i>
                <h3 class="text-lg font-medium text-principal-dark mb-2">No students found</h3>
                <p class="text-principal-silver">Try adjusting your filters to see more results.</p>
              </div>
            </td>
          </tr>
        <% } %>
      </tbody>
    </table>
  </div>

  <!-- Enhanced Pagination -->
  <div class="px-6 py-4 bg-principal-light border-t">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
      <!-- Results Info -->
      <div class="text-sm text-principal-dark">
        Showing <%= (pagination.currentPage - 1) * pagination.limit + 1 %> to
        <%= Math.min(pagination.currentPage * pagination.limit, pagination.totalStudents) %>
        of <%= pagination.totalStudents %> results
      </div>

      <!-- Pagination Controls -->
      <div class="flex flex-col sm:flex-row items-center gap-4">
        <!-- Records per page -->
        <div class="flex items-center gap-2">
          <label class="text-sm text-principal-dark">Show:</label>
          <select id="recordsPerPage" class="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-principal-primary">
            <option value="25" <%= pagination.limit === 25 ? 'selected' : '' %>>25</option>
            <option value="50" <%= pagination.limit === 50 ? 'selected' : '' %>>50</option>
            <option value="100" <%= pagination.limit === 100 ? 'selected' : '' %>>100</option>
            <option value="200" <%= pagination.limit === 200 ? 'selected' : '' %>>200</option>
          </select>
          <span class="text-sm text-principal-dark">per page</span>
        </div>

        <!-- Jump to page -->
        <% if (pagination.totalPages > 1) { %>
        <div class="flex items-center gap-2">
          <label class="text-sm text-principal-dark">Go to page:</label>
          <input type="number" id="jumpToPage" min="1" max="<%= pagination.totalPages %>"
                 value="<%= pagination.currentPage %>"
                 class="w-16 px-2 py-1 border border-gray-300 rounded-md text-sm text-center focus:outline-none focus:ring-2 focus:ring-principal-primary">
          <button id="jumpToPageBtn" class="px-3 py-1 bg-principal-primary text-white rounded-md text-sm hover:bg-principal-hover transition-colors">
            Go
          </button>
        </div>
        <% } %>

        <!-- Page navigation -->
        <% if (pagination.totalPages > 1) { %>
        <div class="flex space-x-1">
          <!-- Previous Page -->
          <% if (pagination.hasPrev) { %>
            <a href="?<%= new URLSearchParams({...filters, page: pagination.currentPage - 1, limit: pagination.limit}).toString() %>"
               class="px-3 py-2 text-sm font-medium text-principal-dark bg-white border border-gray-300 rounded-md hover:bg-principal-light">
              <i class="fas fa-chevron-left"></i>
            </a>
          <% } else { %>
            <span class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
              <i class="fas fa-chevron-left"></i>
            </span>
          <% } %>

          <!-- Page Numbers -->
          <%
            const startPage = Math.max(1, pagination.currentPage - 2);
            const endPage = Math.min(pagination.totalPages, pagination.currentPage + 2);
          %>

          <% if (startPage > 1) { %>
            <a href="?<%= new URLSearchParams({...filters, page: 1, limit: pagination.limit}).toString() %>"
               class="px-3 py-2 text-sm font-medium text-principal-dark bg-white border border-gray-300 rounded-md hover:bg-principal-light">1</a>
            <% if (startPage > 2) { %>
              <span class="px-3 py-2 text-sm font-medium text-principal-dark">...</span>
            <% } %>
          <% } %>

          <% for (let i = startPage; i <= endPage; i++) { %>
            <% if (i === pagination.currentPage) { %>
              <span class="px-3 py-2 text-sm font-medium text-white bg-principal-primary border border-principal-primary rounded-md">
                <%= i %>
              </span>
            <% } else { %>
              <a href="?<%= new URLSearchParams({...filters, page: i, limit: pagination.limit}).toString() %>"
                 class="px-3 py-2 text-sm font-medium text-principal-dark bg-white border border-gray-300 rounded-md hover:bg-principal-light">
                <%= i %>
              </a>
            <% } %>
          <% } %>

          <% if (endPage < pagination.totalPages) { %>
            <% if (endPage < pagination.totalPages - 1) { %>
              <span class="px-3 py-2 text-sm font-medium text-principal-dark">...</span>
            <% } %>
            <a href="?<%= new URLSearchParams({...filters, page: pagination.totalPages, limit: pagination.limit}).toString() %>"
               class="px-3 py-2 text-sm font-medium text-principal-dark bg-white border border-gray-300 rounded-md hover:bg-principal-light">
              <%= pagination.totalPages %>
            </a>
          <% } %>

          <!-- Next Page -->
          <% if (pagination.hasNext) { %>
            <a href="?<%= new URLSearchParams({...filters, page: pagination.currentPage + 1, limit: pagination.limit}).toString() %>"
               class="px-3 py-2 text-sm font-medium text-principal-dark bg-white border border-gray-300 rounded-md hover:bg-principal-light">
              <i class="fas fa-chevron-right"></i>
            </a>
          <% } else { %>
            <span class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
              <i class="fas fa-chevron-right"></i>
            </span>
          <% } %>
        </div>
        <% } %>
      </div>
    </div>
  </div>
</div>

<!-- Export Options Modal -->
<div id="exportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
  <div class="flex items-center justify-center min-h-screen p-4">
    <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-hidden">
      <!-- Modal Header -->
      <div class="bg-principal-primary text-white p-4 rounded-t-lg">
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-medium">
            <i class="fas fa-download mr-2"></i>Export Student Data
          </h3>
          <button id="closeExportModalBtn" class="text-white hover:text-gray-200 p-2 rounded" title="Close">
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>
      </div>

      <!-- Modal Content -->
      <div class="p-6 max-h-96 overflow-y-auto">
        <!-- Export Format Selection -->
        <div class="mb-6">
          <h4 class="text-lg font-semibold text-gray-900 mb-4">Select Export Format</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-principal-primary transition-colors export-format-option" data-format="excel">
              <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <i class="fas fa-file-excel text-2xl text-green-600"></i>
                </div>
                <div>
                  <h5 class="font-semibold text-gray-900">Excel (.xlsx)</h5>
                  <p class="text-sm text-gray-600">Spreadsheet format with formatting</p>
                </div>
              </div>
            </div>
            <div class="border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-principal-primary transition-colors export-format-option" data-format="pdf">
              <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                  <i class="fas fa-file-pdf text-2xl text-red-600"></i>
                </div>
                <div>
                  <h5 class="font-semibold text-gray-900">PDF (.pdf)</h5>
                  <p class="text-sm text-gray-600">Professional document format</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Column Selection -->
        <div class="mb-6">
          <div class="flex justify-between items-center mb-4">
            <h4 class="text-lg font-semibold text-gray-900">Select Columns to Export</h4>
            <div class="flex space-x-2">
              <button id="selectAllColumnsBtn" class="px-3 py-1 bg-principal-primary text-white rounded text-sm hover:bg-principal-hover">
                Select All
              </button>
              <button id="deselectAllColumnsBtn" class="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600">
                Deselect All
              </button>
            </div>
          </div>

          <div class="bg-gray-50 p-4 rounded-lg">
            <p class="text-sm text-gray-600 mb-4">
              <i class="fas fa-info-circle mr-1"></i>
              If no columns are selected, all data will be exported by default.
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              <!-- Basic Information -->
              <div class="space-y-2">
                <h5 class="font-medium text-gray-700 text-sm">Basic Information</h5>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="sno">
                  <span class="text-sm">S.No</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="student_id" checked>
                  <span class="text-sm">Student ID</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="name" checked>
                  <span class="text-sm">Name</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="father_name" checked>
                  <span class="text-sm">Father's Name</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="mother_name" checked>
                  <span class="text-sm">Mother's Name</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="dob">
                  <span class="text-sm">Date of Birth</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="gender" checked>
                  <span class="text-sm">Gender</span>
                </label>
              </div>

              <!-- Academic Information -->
              <div class="space-y-2">
                <h5 class="font-medium text-gray-700 text-sm">Academic Information</h5>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="class" checked>
                  <span class="text-sm">Class</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="section" checked>
                  <span class="text-sm">Section</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="session" checked>
                  <span class="text-sm">Session</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="stream">
                  <span class="text-sm">Stream</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="trade">
                  <span class="text-sm">Trade</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="roll_no" checked>
                  <span class="text-sm">Roll Number</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="medium_name">
                  <span class="text-sm">Medium</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="room_number">
                  <span class="text-sm">Room Number</span>
                </label>
              </div>

              <!-- Contact & Address -->
              <div class="space-y-2">
                <h5 class="font-medium text-gray-700 text-sm">Contact & Address</h5>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="contact_no" checked>
                  <span class="text-sm">Contact Number</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="cur_address">
                  <span class="text-sm">Current Address</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="village_ward">
                  <span class="text-sm">Village/Ward</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="gram_panchayat">
                  <span class="text-sm">Gram Panchayat</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="pin_code">
                  <span class="text-sm">Pin Code</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="district_name">
                  <span class="text-sm">District</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="state_name">
                  <span class="text-sm">State</span>
                </label>
              </div>

              <!-- Administrative -->
              <div class="space-y-2">
                <h5 class="font-medium text-gray-700 text-sm">Administrative</h5>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="udise_code">
                  <span class="text-sm">UDISE Code</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="admission_no">
                  <span class="text-sm">Admission Number</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="admission_date">
                  <span class="text-sm">Admission Date</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="religion_name">
                  <span class="text-sm">Religion</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="caste_category_name">
                  <span class="text-sm">Caste Category</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="is_active">
                  <span class="text-sm">Active Status</span>
                </label>
              </div>

              <!-- Health & Welfare -->
              <div class="space-y-2">
                <h5 class="font-medium text-gray-700 text-sm">Health & Welfare</h5>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="bpl">
                  <span class="text-sm">BPL Status</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="disability">
                  <span class="text-sm">Disability Status</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="height">
                  <span class="text-sm">Height (cm)</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="weight">
                  <span class="text-sm">Weight (kg)</span>
                </label>
              </div>

              <!-- Bank Information -->
              <div class="space-y-2">
                <h5 class="font-medium text-gray-700 text-sm">Bank Information</h5>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="bank_name">
                  <span class="text-sm">Bank Name</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="ifsc_code">
                  <span class="text-sm">IFSC Code</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="account_holder">
                  <span class="text-sm">Account Holder</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="account_holder_name">
                  <span class="text-sm">Account Holder Name</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="account_holder_code">
                  <span class="text-sm">Account Holder Code</span>
                </label>
              </div>

              <!-- System Information -->
              <div class="space-y-2">
                <h5 class="font-medium text-gray-700 text-sm">System Information</h5>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="created_at">
                  <span class="text-sm">Created Date</span>
                </label>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" class="export-column rounded border-gray-300 text-principal-primary" value="updated_at">
                  <span class="text-sm">Last Updated</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
        <div class="flex justify-between items-center">
          <div class="text-sm text-gray-500">
            <span id="exportRecordCount">0</span> records will be exported
          </div>
          <div class="flex space-x-3">
            <button id="cancelExportBtn" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
              <i class="fas fa-times mr-2"></i>Cancel
            </button>
            <button id="confirmExportBtn" class="px-4 py-2 bg-principal-primary text-white rounded-md hover:bg-principal-hover">
              <i class="fas fa-download mr-2"></i>Export Data
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
/* Student Table Styles */
.student-details-expanded {
  background-color: #f8fafc;
}

/* Collapsible filter styles */
#advancedFilters {
  transition: all 0.3s ease;
}

#filterToggleIcon {
  transition: transform 0.2s ease;
}

#filterToggleIcon.rotated {
  transform: rotate(180deg);
}

/* Mobile responsive table */
@media (max-width: 768px) {
  table {
    font-size: 0.875rem;
  }

  .px-6 {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  /* Hide less important columns on mobile */
  table th:nth-child(7),
  table td:nth-child(7),
  table th:nth-child(8),
  table td:nth-child(8) {
    display: none;
  }
}

@media (max-width: 640px) {
  /* Stack student details cards vertically on small screens */
  .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3 {
    grid-template-columns: 1fr !important;
  }
}

/* Smooth animations */
.transition-colors {
  transition: background-color 0.2s ease;
}

/* Button hover effects */
button:hover {
  transform: translateY(-1px);
  transition: transform 0.1s ease;
}
</style>

<!-- Enhanced Student Details Modal with Tabs -->
<div id="studentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
  <div class="flex items-center justify-center min-h-screen p-4">
    <div class="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-screen overflow-hidden">
      <!-- Modal Header -->
      <div class="bg-principal-primary text-white p-4 rounded-t-lg">
        <div class="flex justify-between items-center">
          <h3 id="studentModalTitle" class="text-lg font-medium">
            <i class="fas fa-user-graduate mr-2"></i>Student Details
          </h3>
          <div class="flex items-center space-x-3">
            <button id="downloadStudentPDFBtn" class="text-white hover:text-gray-200 p-2 rounded" title="Download Student Profile">
              <i class="fas fa-download"></i>
            </button>
            <button id="closeStudentModalBtn" class="text-white hover:text-gray-200 p-2 rounded" title="Close">
              <i class="fas fa-times text-xl"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Student General Information (Always Visible) -->
      <div id="studentGeneralInfo" class="px-6 py-4 bg-gray-50 border-b border-gray-200">
        <div class="flex items-center space-x-4">
          <div class="h-16 w-16 rounded-full bg-principal-primary flex items-center justify-center">
            <span id="studentInitials" class="text-xl font-semibold text-white">--</span>
          </div>
          <div class="flex-1">
            <h4 id="studentName" class="text-lg font-semibold text-gray-900">Student Name</h4>
            <p id="studentClass" class="text-sm text-gray-600">Class • Section • Stream</p>
            <p id="studentContact" class="text-sm text-gray-500">Student ID • Contact</p>
          </div>
          <div class="text-right">
            <div class="text-sm text-gray-500">
              <span id="studentId">Student ID</span>
            </div>
            <div class="text-xs text-gray-400">
              <span id="studentSession">Session</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Student Score Cards -->
      <div id="studentScoreCards" class="px-6 py-4 bg-white border-b border-gray-200">
        <div class="score-cards-grid">
          <!-- Profile Completion Card -->
          <div class="score-card">
            <div class="score-header">
              <div>
                <h5 class="score-title">Profile Completion</h5>
                <p class="score-subtitle">Data completeness</p>
              </div>
            </div>
            <div class="score-content">
              <div class="score-details">
                <h3 id="studentProfileCompletionValue" class="score-value">0%</h3>
                <p id="studentProfileCompletionDescription" class="score-description">Complete student profile</p>
              </div>
              <div id="studentProfileCompletionCircle" class="circular-progress medium" style="--progress: 0">
                <span class="progress-text">0%</span>
              </div>
            </div>
          </div>

          <!-- Rating Score Card -->
          <div class="score-card">
            <div class="score-header">
              <div>
                <h5 class="score-title">Academic Rating</h5>
                <p class="score-subtitle">Overall assessment</p>
              </div>
            </div>
            <div class="score-content">
              <div class="score-details">
                <h3 id="studentRatingScoreValue" class="score-value">0%</h3>
                <p id="studentRatingScoreDescription" class="score-description">Academic evaluation</p>
              </div>
              <div id="studentRatingScoreCircle" class="circular-progress medium" style="--progress: 0">
                <span class="progress-text">0%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Tab Navigation -->
      <div class="border-b border-gray-200">
        <nav class="flex space-x-8 px-6" aria-label="Tabs">
          <button id="personalTab" class="student-tab-btn border-b-2 border-principal-primary text-principal-primary py-4 px-1 text-sm font-medium" data-tab="personal">
            <i class="fas fa-user mr-2"></i>Personal Information
          </button>
          <button id="academicTab" class="student-tab-btn border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-4 px-1 text-sm font-medium" data-tab="academic">
            <i class="fas fa-graduation-cap mr-2"></i>Academic Details
          </button>
          <button id="contactTab" class="student-tab-btn border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-4 px-1 text-sm font-medium" data-tab="contact">
            <i class="fas fa-map-marker-alt mr-2"></i>Contact & Address
          </button>
          <button id="administrativeTab" class="student-tab-btn border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-4 px-1 text-sm font-medium" data-tab="administrative">
            <i class="fas fa-file-alt mr-2"></i>Administrative
          </button>
          <button id="healthTab" class="student-tab-btn border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-4 px-1 text-sm font-medium" data-tab="health">
            <i class="fas fa-heartbeat mr-2"></i>Health & Welfare
          </button>
        </nav>
      </div>

      <!-- Tab Content -->
      <div class="max-h-96 overflow-y-auto">
        <!-- Personal Information Tab -->
        <div id="personalTabContent" class="student-tab-content p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <h5 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h5>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Full Name:</span>
                  <span id="personalName" class="text-sm text-gray-900">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Date of Birth:</span>
                  <span id="personalDOB" class="text-sm text-gray-900">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Gender:</span>
                  <span id="personalGender" class="text-sm text-gray-900">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Religion:</span>
                  <span id="personalReligion" class="text-sm text-gray-900">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Caste Category:</span>
                  <span id="personalCaste" class="text-sm text-gray-900">-</span>
                </div>
              </div>
            </div>
            <div class="space-y-4">
              <h5 class="text-lg font-semibold text-gray-900 mb-4">Family Information</h5>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Father's Name:</span>
                  <span id="personalFatherName" class="text-sm text-gray-900">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Mother's Name:</span>
                  <span id="personalMotherName" class="text-sm text-gray-900">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Contact Number:</span>
                  <span id="personalContact" class="text-sm text-gray-900">-</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Academic Details Tab -->
        <div id="academicTabContent" class="student-tab-content p-6 hidden">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <h5 class="text-lg font-semibold text-gray-900 mb-4">Current Academic Status</h5>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Class:</span>
                  <span id="academicClass" class="text-sm text-gray-900">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Section:</span>
                  <span id="academicSection" class="text-sm text-gray-900">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Stream:</span>
                  <span id="academicStream" class="text-sm text-gray-900">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Trade:</span>
                  <span id="academicTrade" class="text-sm text-gray-900">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Roll Number:</span>
                  <span id="academicRollNo" class="text-sm text-gray-900">-</span>
                </div>
              </div>
            </div>
            <div class="space-y-4">
              <h5 class="text-lg font-semibold text-gray-900 mb-4">Academic Session</h5>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Session:</span>
                  <span id="academicSession" class="text-sm text-gray-900">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Medium:</span>
                  <span id="academicMedium" class="text-sm text-gray-900">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Room Number:</span>
                  <span id="academicRoom" class="text-sm text-gray-900">-</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Contact & Address Tab -->
        <div id="contactTabContent" class="student-tab-content p-6 hidden">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <h5 class="text-lg font-semibold text-gray-900 mb-4">Address Information</h5>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Current Address:</span>
                  <span id="contactAddress" class="text-sm text-gray-900">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Village/Ward:</span>
                  <span id="contactVillage" class="text-sm text-gray-900">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Gram Panchayat:</span>
                  <span id="contactGramPanchayat" class="text-sm text-gray-900">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Pin Code:</span>
                  <span id="contactPinCode" class="text-sm text-gray-900">-</span>
                </div>
              </div>
            </div>
            <div class="space-y-4">
              <h5 class="text-lg font-semibold text-gray-900 mb-4">Location Details</h5>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">District:</span>
                  <span id="contactDistrict" class="text-sm text-gray-900">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">State:</span>
                  <span id="contactState" class="text-sm text-gray-900">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Contact Number:</span>
                  <span id="contactPhone" class="text-sm text-gray-900">-</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Administrative Tab -->
        <div id="administrativeTabContent" class="student-tab-content p-6 hidden">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <h5 class="text-lg font-semibold text-gray-900 mb-4">Admission Details</h5>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Admission Number:</span>
                  <span id="adminAdmissionNo" class="text-sm text-gray-900">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Admission Date:</span>
                  <span id="adminAdmissionDate" class="text-sm text-gray-900">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">UDISE Code:</span>
                  <span id="adminUdiseCode" class="text-sm text-gray-900">-</span>
                </div>
              </div>
            </div>
            <div class="space-y-4">
              <h5 class="text-lg font-semibold text-gray-900 mb-4">Financial Information</h5>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Bank Name:</span>
                  <span id="adminBankName" class="text-sm text-gray-900">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">IFSC Code:</span>
                  <span id="adminIFSC" class="text-sm text-gray-900">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Account Holder:</span>
                  <span id="adminAccountHolder" class="text-sm text-gray-900">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Account Holder Code:</span>
                  <span id="adminAccountCode" class="text-sm text-gray-900">-</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Health & Welfare Tab -->
        <div id="healthTabContent" class="student-tab-content p-6 hidden">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <h5 class="text-lg font-semibold text-gray-900 mb-4">Physical Information</h5>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Height:</span>
                  <span id="healthHeight" class="text-sm text-gray-900">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Weight:</span>
                  <span id="healthWeight" class="text-sm text-gray-900">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Disability Status:</span>
                  <span id="healthDisability" class="text-sm text-gray-900">-</span>
                </div>
              </div>
            </div>
            <div class="space-y-4">
              <h5 class="text-lg font-semibold text-gray-900 mb-4">Welfare Information</h5>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">BPL Status:</span>
                  <span id="healthBPL" class="text-sm text-gray-900">-</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
        <div class="flex justify-between items-center">
          <div class="text-sm text-gray-500">
            <span>Last updated: <span id="lastUpdated">-</span></span>
          </div>
          <div class="flex space-x-3">
            <button id="closeStudentModalFooterBtn" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
              <i class="fas fa-times mr-2"></i>Close
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>



<script>
document.addEventListener('DOMContentLoaded', function() {
  // Filter toggle functionality
  const toggleFilters = document.getElementById('toggleFilters');
  const advancedFilters = document.getElementById('advancedFilters');
  const filterToggleIcon = document.getElementById('filterToggleIcon');

  if (toggleFilters && advancedFilters && filterToggleIcon) {
    toggleFilters.addEventListener('click', function() {
      if (advancedFilters.classList.contains('hidden')) {
        advancedFilters.classList.remove('hidden');
        filterToggleIcon.classList.add('rotate-180');
      } else {
        advancedFilters.classList.add('hidden');
        filterToggleIcon.classList.remove('rotate-180');
      }
    });
  }

  // Quick filters functionality
  const quickFilters = document.querySelectorAll('.quick-filter');
  const filterForm = document.getElementById('filterForm');

  quickFilters.forEach(filter => {
    filter.addEventListener('click', function() {
      const filterType = this.dataset.filter;

      // Reset all filters
      const inputs = filterForm.querySelectorAll('input, select');
      inputs.forEach(input => {
        if (input.name !== 'limit') {
          input.value = '';
        }
      });

      // Apply specific filter
      switch(filterType) {
        case 'male':
          document.querySelector('select[name="gender"]').value = 'Male';
          break;
        case 'female':
          document.querySelector('select[name="gender"]').value = 'Female';
          break;
        case 'bpl':
          document.querySelector('select[name="bpl"]').value = 'Yes';
          break;
        case 'disability':
          document.querySelector('select[name="disability"]').value = 'Yes';
          break;
        case 'science':
          document.querySelector('select[name="stream"]').value = 'Science';
          break;
        case 'commerce':
          document.querySelector('select[name="stream"]').value = 'Commerce';
          break;
      }

      // Submit form
      if (filterType !== 'all') {
        filterForm.submit();
      }
    });
  });

  // Trash button functionality
  const trashBtn = document.getElementById('trashBtn');
  if (trashBtn) {
    trashBtn.addEventListener('click', function() {
      openTrashModal();
    });
  }

  // Clear filters functionality
  const clearFilters = document.getElementById('clearFilters');
  if (clearFilters) {
    clearFilters.addEventListener('click', function() {
      const inputs = filterForm.querySelectorAll('input, select');
      inputs.forEach(input => {
        if (input.name !== 'limit') {
          input.value = '';
        }
      });
      filterForm.submit();
    });
  }
});

// Modal functions are now handled by the external StudentsPage JavaScript


</script>
